<template>
  <div>
    <BaseStyles />
    <Banner>
      This is a playground to test local changes for PPP (Perfect Profile Picture). 
      For suggestions, issues or PR's go to the 
      <a style="color: white" href="https://github.com/rootasjey/ppp">
        GitHub repository
      </a>
    </Banner>
    
    <Header>
      <SegmentGroup>
        <Segment
          v-for="variantItem in variants"
          :key="variantItem"
          @click="variant = variantItem"
          :is-selected="variantItem === variant"
        >
          {{ variantItem }}
        </Segment>
      </SegmentGroup>
      
      <ColorsSection>
        <ColorDot 
          v-for="(color, index) in filteredColors" 
          :key="index"
          :value="color" 
          @change="(newColor) => updateColor(index, newColor)" 
        />
      </ColorsSection>

      <Button @click="handleRandomColors">Random palette</Button>
      <Button @click="isSquare = !isSquare">{{ isSquare ? 'Round' : 'Square' }}</Button>
      
      <SegmentGroup>
        <SizeDot
          v-for="(size, index) in Object.values(avatarSizes)"
          :key="index"
          @click="avatarSize = size"
          :is-selected="size === avatarSize"
          :size="size"
        />
      </SegmentGroup>
    </Header>
    
    <AvatarsGrid>
      <AvatarWrapper
        v-for="(exampleName, index) in exampleNames"
        :key="index"
        :size="avatarSize"
        :square="isSquare"
        :name="exampleName"
        :playground-colors="filteredColors"
        :variant="variant"
      />
    </AvatarsGrid>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import colors from 'nice-color-palettes/1000.json'
import { exampleNames } from '~/data/example-names'
import Avatar from '../../lib/components/Avatar.vue'

// Import UI components
import BaseStyles from '~/components/ui/BaseStyles.vue'
import Banner from '~/components/ui/Banner.vue'
import Header from '~/components/ui/Header.vue'
import SegmentGroup from '~/components/ui/SegmentGroup.vue'
import Segment from '~/components/ui/Segment.vue'
import Button from '~/components/ui/Button.vue'
import ColorDot from '~/components/ui/ColorDot.vue'
import ColorsSection from '~/components/ui/ColorsSection.vue'
import AvatarsGrid from '~/components/ui/AvatarsGrid.vue'
import AvatarWrapper from '~/components/AvatarWrapper.vue'
import SizeDot from '~/components/ui/SizeDot.vue'

const paletteColors = colors

const variants = ['beam', 'bauhaus', 'ring', 'sunset', 'pixel', 'marble'] as const
const avatarSizes = {
  small: 40,
  medium: 80,
  large: 128,
}

// Reactive state
const defaultPlaygroundColors = paletteColors[493]
const playgroundColors = ref(defaultPlaygroundColors)
const filteredColors = ref([...defaultPlaygroundColors])
const avatarSize = ref(avatarSizes.medium)
const variant = ref<typeof variants[number]>('beam')
const isSquare = ref(false)

// Methods
const getRandomPaletteIndex = () => Math.floor(Math.random() * paletteColors.length)

const handleRandomColors = () => {
  const newColors = paletteColors[getRandomPaletteIndex()]
  playgroundColors.value = newColors
  filteredColors.value = [...newColors]
}

const updateColor = (index: number, newColor: string) => {
  filteredColors.value[index] = newColor
}

// Initialize colors
onMounted(() => {
  filteredColors.value = [...playgroundColors.value]
})
</script>
