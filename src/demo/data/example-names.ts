// Names from https://notablewomen.withgoogle.com/all
export const exampleNames = [
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  'Sojourner Truth',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>ga<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON> <PERSON>',
  '<PERSON> <PERSON>in<PERSON>',
  '<PERSON><PERSON> <PERSON>',
  '<PERSON> <PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON> <PERSON><PERSON>',
  '<PERSON> <PERSON><PERSON>',
  '<PERSON>',
  '<PERSON><PERSON> <PERSON>',
  '<PERSON> <PERSON>',
  '<PERSON> <PERSON>',
  '<PERSON>',
  '<PERSON> <PERSON>',
  'Jane Johnston',
  'Alice Childress',
  'Georgia O',
  'Rebecca Crumpler',
  'Anne Bradstreet',
  'Elizabeth Blackwell',
  'Christa McAuliffe',
  'Edmonia Lewis',
  'Nellie Bly',
  'Mary Cassatt',
  'Pauli Murray',
  'Ellen Swallow',
  'Hedy Lamarr',
  'Pearl Kendrick',
  'Abigail Adams',
  'Margaret Fuller',
  'Emma Lazarus',
  'Marian Anderson',
  'Virginia Apgar',
  'Mary Walton',
];
