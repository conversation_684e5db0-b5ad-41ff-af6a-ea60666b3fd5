# PPP (Perfect Profile Picture)

PPP (Perfect Profile Picture) is a tiny JavaScript Vue.js/Nuxt.js library that generates custom, SVG-based avatars from any username and color palette.

<a href="https://www.npmjs.com/package/ppp">

![hi](https://badgen.net/npm/v/ppp)

</a>

## Install

```bash
npm install ppp
```

## Usage

### Vue.js

```vue
<template>
  <Avatar name="<PERSON>" />
</template>

<script setup>
import Avatar from 'ppp';
</script>
```

### Nuxt.js

```vue
<template>
  <Avatar name="<PERSON>" />
</template>

<script setup>
import Avatar from 'ppp';
</script>
```

### Props

| Prop    | Type                                                         | Default                                                   |
|---------|--------------------------------------------------------------|-----------------------------------------------------------|
| size    | number or string                                             | `40px`                                                    |
| square  | boolean                                                      | `false`                                                   |
| title   | boolean                                                      | `false`                                                   |
| name    | string                                                       | `Clara Barton`                                            |
| variant | oneOf: `marble`, `beam`, `pixel`,`sunset`, `ring`, `bauhaus` | `marble`                                                  |
| colors  | array                                                        | `['#92A1C6', '#146A7C', '#F0AB3D', '#C271B4', '#C20D90']` | 


#### Name
The `name` prop is used to generate the avatar. It can be the username, email or any random string.

```vue
<Avatar name="Maria Mitchell"/>
```

#### Variant
The `variant` prop is used to change the theme of the avatar. The available variants are: `marble`, `beam`, `pixel`, `sunset`, `ring` and `bauhaus`.

```vue
<Avatar name="Alice Paul" variant="beam"/>
```

#### Size
The `size` prop is used to change the size of the avatar.

```vue
<Avatar name="Ada Lovelace" :size="88"/>
```

#### Colors
The `colors` prop is used to change the color palette of the avatar.

```vue
<Avatar name="Grace Hopper" :colors="['#fb6900', '#f63700', '#004853', '#007e80', '#00b9bd']"/>
```

#### Square
The `square` prop is used to make the avatar square.

```vue
<Avatar name="Helen Keller" square/>
```

## Features

- 🎨 **6 unique avatar styles**: marble, beam, pixel, sunset, ring, and bauhaus
- 🎯 **Deterministic**: Same name always generates the same avatar
- 🎨 **Customizable colors**: Use your own color palette
- 📦 **Lightweight**: Minimal bundle size
- 🔧 **TypeScript support**: Full type definitions included
- ⚡ **Vue 3 & Nuxt 3**: Built for modern Vue.js ecosystem
- 🎭 **Accessible**: Proper ARIA labels and semantic markup

## Migration from boring-avatars

If you're migrating from the React `boring-avatars` library:

1. Replace `boring-avatars` with `ppp` in your package.json
2. Update import statements to use Vue.js syntax
3. Convert JSX props to Vue.js template syntax (`:size` instead of `size={value}`)
4. The API remains largely the same for easy migration

## License

MIT License - feel free to use in your projects!
