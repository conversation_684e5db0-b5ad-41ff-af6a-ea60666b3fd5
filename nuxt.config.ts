// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: true },
  
  // Configure the demo app
  srcDir: 'src/demo/',
  
  // CSS configuration
  css: ['~/assets/css/main.css'],
  
  // TypeScript configuration
  typescript: {
    typeCheck: true
  },
  
  // Build configuration for library
  nitro: {
    experimental: {
      wasm: true
    }
  },
  
  // Vite configuration
  vite: {
    optimizeDeps: {
      include: ['nice-color-palettes']
    }
  },
  
  // App configuration
  app: {
    head: {
      title: 'PPP - Perfect Profile Picture',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { 
          name: 'description', 
          content: 'PPP (Perfect Profile Picture) generates custom, SVG-based avatars from any username and color palette.' 
        }
      ]
    }
  }
})
