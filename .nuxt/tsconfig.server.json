{"compilerOptions": {"forceConsistentCasingInFileNames": true, "strict": true, "noEmit": true, "skipLibCheck": true, "target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "allowJs": true, "resolveJsonModule": true, "jsx": "preserve", "allowSyntheticDefaultImports": true, "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "paths": {"#imports": ["./types/nitro-imports"], "~/*": ["../src/demo/*"], "@/*": ["../src/demo/*"], "~~/*": ["../*"], "@@/*": ["../*"], "nitropack/types": ["../node_modules/nitropack/types"], "nitropack": ["../node_modules/nitropack"], "defu": ["../node_modules/defu"], "h3": ["../node_modules/h3"], "consola": ["../node_modules/consola"], "ofetch": ["../node_modules/ofetch"], "@unhead/vue": ["../node_modules/@unhead/vue"], "@vue/runtime-core": ["../node_modules/@vue/runtime-core"], "@vue/compiler-sfc": ["../node_modules/@vue/compiler-sfc"], "vue-router": ["../node_modules/vue-router"], "vue-router/auto-routes": ["../node_modules/vue-router/vue-router-auto-routes"], "unplugin-vue-router/client": ["../node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../node_modules/@nuxt/schema"], "nuxt": ["../node_modules/nuxt"], "~": ["../src/demo"], "@": ["../src/demo"], "~~": ["./.."], "@@": ["./.."], "#shared": ["../shared"], "assets": ["../src/demo/assets"], "public": ["../src/demo/public"], "#build": ["./"], "#build/*": ["./*"], "#internal/nuxt/paths": ["../node_modules/nuxt/dist/core/runtime/nitro/paths"], "#vue-router": ["./vue-router"]}, "lib": ["esnext", "webworker", "dom.iterable"]}, "include": ["./types/nitro-nuxt.d.ts", "../node_modules/@nuxt/telemetry/runtime/server", "./types/nitro.d.ts", "../**/*", "../src/demo/server/**/*"], "exclude": ["../node_modules", "../node_modules/nuxt/node_modules", "../dist"]}