{"name": "boring-avatars", "description": "Boring avatars is a tiny JavaScript React library that generates custom, SVG-based, round avatars from any username and color palette.", "homepage": "https://boringavatars.com/", "version": "2.0.1", "repository": {"type": "git", "url": "https://github.com/boringdesigners/boring-avatars"}, "bugs": {"url": "https://github.com/boringdesigners/boring-avatars/issues"}, "keywords": ["avatar", "gravatar", "profile picture", "user avatar", "avatar generator", "avatar placeholder"], "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/*"], "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build": "tsc --p ./tsconfig.lib.json && vite build", "lint": "eslint .", "preview": "vite preview"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-color": "^3.0.13", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "nice-color-palettes": "^4.0.0", "react": "^19.1.0", "react-color": "^2.19.3", "react-dom": "^19.1.0", "styled-components": "^6.1.19", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite-plugin-dts": "^4.5.4", "vite": "^7.0.0"}}