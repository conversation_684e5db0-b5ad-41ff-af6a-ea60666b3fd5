{"name": "ppp", "description": "PPP (Perfect Profile Picture) is a tiny JavaScript Vue.js/Nuxt.js library that generates custom, SVG-based, round avatars from any username and color palette.", "homepage": "https://github.com/rootasjey/ppp", "version": "1.0.0", "repository": {"type": "git", "url": "https://github.com/rootasjey/ppp"}, "bugs": {"url": "https://github.com/rootasjey/ppp/issues"}, "keywords": ["avatar", "gravatar", "profile picture", "user avatar", "avatar generator", "avatar placeholder", "vue", "nuxt", "vue3", "nuxt3", "perfect profile picture"], "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/*"], "license": "MIT", "type": "module", "scripts": {"dev": "nuxt dev", "build": "tsc --p ./tsconfig.lib.json && vite build", "build:demo": "nuxt build", "lint": "eslint .", "preview": "nuxt preview", "generate": "nuxt generate"}, "peerDependencies": {"vue": ">=3.0.0", "nuxt": ">=3.0.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@nuxt/devtools": "latest", "@nuxt/eslint-config": "^0.2.0", "@types/node": "^24.1.0", "@vue/eslint-config-typescript": "^13.0.0", "eslint": "^9.29.0", "eslint-plugin-vue": "^9.20.1", "globals": "^16.2.0", "nice-color-palettes": "^4.0.0", "nuxt": "^3.13.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite-plugin-dts": "^4.5.4", "vite": "^7.0.0", "vue": "^3.4.0", "vue-tsc": "^2.0.0"}}